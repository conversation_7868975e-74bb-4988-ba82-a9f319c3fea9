-- 支付宝退款记录表
CREATE TABLE `alipay_refund_record` (
  `refund_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '退款记录ID',
  `order_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '支付宝交易号',
  `refund_no` varchar(64) NOT NULL COMMENT '退款单号(商户生成)',
  `alipay_refund_no` varchar(64) DEFAULT NULL COMMENT '支付宝退款单号',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_reason` varchar(256) DEFAULT NULL COMMENT '退款原因',
  `refund_status` varchar(32) DEFAULT 'PROCESSING' COMMENT '退款状态(PROCESSING-处理中,SUCCESS-成功,FAILED-失败)',
  `request_data` text COMMENT '退款请求数据(JSON格式)',
  `response_data` text COMMENT '退款响应数据(JSON格式)',
  `error_msg` varchar(512) DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`refund_id`),
  UNIQUE KEY `uk_refund_no` (`refund_no`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_trade_no` (`trade_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付宝退款记录表';

-- 支付宝通知记录表
CREATE TABLE `alipay_notify_record` (
  `notify_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通知记录ID',
  `notify_type` varchar(32) NOT NULL COMMENT '通知类型(REFUND-退款通知,PAYMENT-支付通知)',
  `order_no` varchar(64) DEFAULT NULL COMMENT '商户订单号',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '支付宝交易号',
  `refund_no` varchar(64) DEFAULT NULL COMMENT '退款单号',
  `notify_data` text NOT NULL COMMENT '通知数据(JSON格式)',
  `notify_status` varchar(32) DEFAULT 'PENDING' COMMENT '处理状态(PENDING-待处理,SUCCESS-处理成功,FAILED-处理失败)',
  `process_result` text DEFAULT NULL COMMENT '处理结果',
  `error_msg` varchar(512) DEFAULT NULL COMMENT '错误信息',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`notify_id`),
  KEY `idx_notify_type` (`notify_type`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_trade_no` (`trade_no`),
  KEY `idx_refund_no` (`refund_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付宝通知记录表';
