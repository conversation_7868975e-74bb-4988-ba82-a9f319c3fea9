@echo off
echo SSH Tunnel
echo ==========================

set JUMP_HOST=*************
set JUMP_USER=tunneluser
set JUMP_PORT=33433

set REDIS_LOCAL_PORT=6380
set REDIS_REMOTE_HOST=************
set REDIS_REMOTE_PORT=6379

set PPK_KEY_PATH=..\scc_tunneluser.ppk


echo This should connect successfully and then wait for Enter key.
echo After you see "Access granted. Press Return to begin session.", press Enter.
echo.
pause

echo Starting tunnel...
echo LOCAL_PORT=6380
plink -ssh -L %REDIS_LOCAL_PORT%:%REDIS_REMOTE_HOST%:%REDIS_REMOTE_PORT% -P %JUMP_PORT% -i "%PPK_KEY_PATH%" %JUMP_USER%@%JUMP_HOST% -N
