@echo off
echo SSH Tunnel
echo ==========================

set JUMP_HOST=*************
set JUMP_USER=tunneluser
set JUMP_PORT=33433

set LOCAL_PORT=3307
set REMOTE_HOST=************
set REMOTE_PORT=3306
set PPK_KEY_PATH=..\scc_tunneluser.ppk


echo This should connect successfully and then wait for Enter key.
echo After you see "Access granted. Press Return to begin session.", press Enter.
echo.
pause

echo Starting tunnel...
echo LOCAL_PORT=3307
plink -ssh -L %LOCAL_PORT%:%REMOTE_HOST%:%REMOTE_PORT% -P %JUMP_PORT% -i "%PPK_KEY_PATH%" %JUMP_USER%@%JUMP_HOST% -N
