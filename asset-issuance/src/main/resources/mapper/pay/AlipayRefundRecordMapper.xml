<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.pay.mapper.AlipayRefundRecordMapper">
    
    <resultMap type="AlipayRefundRecord" id="AlipayRefundRecordResult">
        <result property="refundId"    column="refund_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="tradeNo"    column="trade_no"    />
        <result property="refundNo"    column="refund_no"    />
        <result property="alipayRefundNo"    column="alipay_refund_no"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="refundStatus"    column="refund_status"    />
        <result property="requestData"    column="request_data"    />
        <result property="responseData"    column="response_data"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAlipayRefundRecordVo">
        select refund_id, order_no, trade_no, refund_no, alipay_refund_no, refund_amount, refund_reason, refund_status, request_data, response_data, error_msg, create_time, update_time, create_by, update_by from alipay_refund_record
    </sql>

    <select id="selectAlipayRefundRecordList" parameterType="AlipayRefundRecord" resultMap="AlipayRefundRecordResult">
        <include refid="selectAlipayRefundRecordVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
            <if test="refundNo != null  and refundNo != ''"> and refund_no = #{refundNo}</if>
            <if test="alipayRefundNo != null  and alipayRefundNo != ''"> and alipay_refund_no = #{alipayRefundNo}</if>
            <if test="refundAmount != null "> and refund_amount = #{refundAmount}</if>
            <if test="refundReason != null  and refundReason != ''"> and refund_reason like concat('%', #{refundReason}, '%')</if>
            <if test="refundStatus != null  and refundStatus != ''"> and refund_status = #{refundStatus}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAlipayRefundRecordByRefundId" parameterType="Long" resultMap="AlipayRefundRecordResult">
        <include refid="selectAlipayRefundRecordVo"/>
        where refund_id = #{refundId}
    </select>

    <select id="selectByRefundNo" parameterType="String" resultMap="AlipayRefundRecordResult">
        <include refid="selectAlipayRefundRecordVo"/>
        where refund_no = #{refundNo}
    </select>

    <select id="selectByOrderNo" parameterType="String" resultMap="AlipayRefundRecordResult">
        <include refid="selectAlipayRefundRecordVo"/>
        where order_no = #{orderNo}
        order by create_time desc
    </select>

    <insert id="insertAlipayRefundRecord" parameterType="AlipayRefundRecord" useGeneratedKeys="true" keyProperty="refundId">
        insert into alipay_refund_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="refundNo != null and refundNo != ''">refund_no,</if>
            <if test="alipayRefundNo != null">alipay_refund_no,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="refundStatus != null">refund_status,</if>
            <if test="requestData != null">request_data,</if>
            <if test="responseData != null">response_data,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="refundNo != null and refundNo != ''">#{refundNo},</if>
            <if test="alipayRefundNo != null">#{alipayRefundNo},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="refundStatus != null">#{refundStatus},</if>
            <if test="requestData != null">#{requestData},</if>
            <if test="responseData != null">#{responseData},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAlipayRefundRecord" parameterType="AlipayRefundRecord">
        update alipay_refund_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="refundNo != null and refundNo != ''">refund_no = #{refundNo},</if>
            <if test="alipayRefundNo != null">alipay_refund_no = #{alipayRefundNo},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundReason != null">refund_reason = #{refundReason},</if>
            <if test="refundStatus != null">refund_status = #{refundStatus},</if>
            <if test="requestData != null">request_data = #{requestData},</if>
            <if test="responseData != null">response_data = #{responseData},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where refund_id = #{refundId}
    </update>

    <delete id="deleteAlipayRefundRecordByRefundId" parameterType="Long">
        delete from alipay_refund_record where refund_id = #{refundId}
    </delete>

    <delete id="deleteAlipayRefundRecordByRefundIds" parameterType="String">
        delete from alipay_refund_record where refund_id in 
        <foreach item="refundId" collection="array" open="(" separator="," close=")">
            #{refundId}
        </foreach>
    </delete>
</mapper>
