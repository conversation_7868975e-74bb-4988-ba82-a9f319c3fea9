<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.pay.mapper.AlipayNotifyRecordMapper">
    
    <resultMap type="AlipayNotifyRecord" id="AlipayNotifyRecordResult">
        <result property="notifyId"    column="notify_id"    />
        <result property="notifyType"    column="notify_type"    />
        <result property="orderNo"    column="order_no"    />
        <result property="tradeNo"    column="trade_no"    />
        <result property="refundNo"    column="refund_no"    />
        <result property="notifyData"    column="notify_data"    />
        <result property="notifyStatus"    column="notify_status"    />
        <result property="processResult"    column="process_result"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="retryCount"    column="retry_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAlipayNotifyRecordVo">
        select notify_id, notify_type, order_no, trade_no, refund_no, notify_data, notify_status, process_result, error_msg, retry_count, create_time, update_time from alipay_notify_record
    </sql>

    <select id="selectAlipayNotifyRecordList" parameterType="AlipayNotifyRecord" resultMap="AlipayNotifyRecordResult">
        <include refid="selectAlipayNotifyRecordVo"/>
        <where>  
            <if test="notifyType != null  and notifyType != ''"> and notify_type = #{notifyType}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="tradeNo != null  and tradeNo != ''"> and trade_no = #{tradeNo}</if>
            <if test="refundNo != null  and refundNo != ''"> and refund_no = #{refundNo}</if>
            <if test="notifyStatus != null  and notifyStatus != ''"> and notify_status = #{notifyStatus}</if>
            <if test="retryCount != null "> and retry_count = #{retryCount}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAlipayNotifyRecordByNotifyId" parameterType="Long" resultMap="AlipayNotifyRecordResult">
        <include refid="selectAlipayNotifyRecordVo"/>
        where notify_id = #{notifyId}
    </select>

    <select id="selectByOrderNoAndType" resultMap="AlipayNotifyRecordResult">
        <include refid="selectAlipayNotifyRecordVo"/>
        where order_no = #{orderNo} and notify_type = #{notifyType}
        order by create_time desc
    </select>

    <insert id="insertAlipayNotifyRecord" parameterType="AlipayNotifyRecord" useGeneratedKeys="true" keyProperty="notifyId">
        insert into alipay_notify_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="notifyType != null and notifyType != ''">notify_type,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="tradeNo != null">trade_no,</if>
            <if test="refundNo != null">refund_no,</if>
            <if test="notifyData != null">notify_data,</if>
            <if test="notifyStatus != null">notify_status,</if>
            <if test="processResult != null">process_result,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="notifyType != null and notifyType != ''">#{notifyType},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="tradeNo != null">#{tradeNo},</if>
            <if test="refundNo != null">#{refundNo},</if>
            <if test="notifyData != null">#{notifyData},</if>
            <if test="notifyStatus != null">#{notifyStatus},</if>
            <if test="processResult != null">#{processResult},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAlipayNotifyRecord" parameterType="AlipayNotifyRecord">
        update alipay_notify_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="notifyType != null and notifyType != ''">notify_type = #{notifyType},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="tradeNo != null">trade_no = #{tradeNo},</if>
            <if test="refundNo != null">refund_no = #{refundNo},</if>
            <if test="notifyData != null">notify_data = #{notifyData},</if>
            <if test="notifyStatus != null">notify_status = #{notifyStatus},</if>
            <if test="processResult != null">process_result = #{processResult},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where notify_id = #{notifyId}
    </update>

    <delete id="deleteAlipayNotifyRecordByNotifyId" parameterType="Long">
        delete from alipay_notify_record where notify_id = #{notifyId}
    </delete>

    <delete id="deleteAlipayNotifyRecordByNotifyIds" parameterType="String">
        delete from alipay_notify_record where notify_id in 
        <foreach item="notifyId" collection="array" open="(" separator="," close=")">
            #{notifyId}
        </foreach>
    </delete>
</mapper>
