package com.ruoyi.issue.pay.domain;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;

/**
 * 支付宝通知记录对象 alipay_notify_record
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@ApiModel(value = "支付宝通知记录对象")
@TableName("alipay_notify_record")
public class AlipayNotifyRecord {
    private static final long serialVersionUID = 1L;

    /** 通知记录ID */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "通知记录ID")
    private Long notifyId;

    /** 通知类型 */
    @Excel(name = "通知类型")
    @ApiModelProperty(value = "通知类型(REFUND-退款通知,PAYMENT-支付通知)")
    private String notifyType;

    /** 商户订单号 */
    @Excel(name = "商户订单号")
    @ApiModelProperty(value = "商户订单号")
    private String orderNo;

    /** 支付宝交易号 */
    @Excel(name = "支付宝交易号")
    @ApiModelProperty(value = "支付宝交易号")
    private String tradeNo;

    /** 退款单号 */
    @Excel(name = "退款单号")
    @ApiModelProperty(value = "退款单号")
    private String refundNo;

    /** 通知数据 */
    @ApiModelProperty(value = "通知数据(JSON格式)")
    private String notifyData;

    /** 处理状态 */
    @Excel(name = "处理状态")
    @ApiModelProperty(value = "处理状态(PENDING-待处理,SUCCESS-处理成功,FAILED-处理失败)")
    private String notifyStatus;

    /** 处理结果 */
    @ApiModelProperty(value = "处理结果")
    private String processResult;

    /** 错误信息 */
    @Excel(name = "错误信息")
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    /** 重试次数 */
    @Excel(name = "重试次数")
    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
