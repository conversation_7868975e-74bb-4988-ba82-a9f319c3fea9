package com.ruoyi.issue.pay.service;

import java.util.List;
import com.ruoyi.issue.pay.domain.AlipayRefundRecord;

/**
 * 支付宝退款记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface AlipayRefundRecordService {
    
    /**
     * 查询支付宝退款记录
     * 
     * @param refundId 支付宝退款记录主键
     * @return 支付宝退款记录
     */
    public AlipayRefundRecord selectAlipayRefundRecordByRefundId(Long refundId);

    /**
     * 查询支付宝退款记录列表
     * 
     * @param alipayRefundRecord 支付宝退款记录
     * @return 支付宝退款记录集合
     */
    public List<AlipayRefundRecord> selectAlipayRefundRecordList(AlipayRefundRecord alipayRefundRecord);

    /**
     * 新增支付宝退款记录
     * 
     * @param alipayRefundRecord 支付宝退款记录
     * @return 结果
     */
    public int insertAlipayRefundRecord(AlipayRefundRecord alipayRefundRecord);

    /**
     * 修改支付宝退款记录
     * 
     * @param alipayRefundRecord 支付宝退款记录
     * @return 结果
     */
    public int updateAlipayRefundRecord(AlipayRefundRecord alipayRefundRecord);

    /**
     * 批量删除支付宝退款记录
     * 
     * @param refundIds 需要删除的支付宝退款记录主键集合
     * @return 结果
     */
    public int deleteAlipayRefundRecordByRefundIds(Long[] refundIds);

    /**
     * 删除支付宝退款记录信息
     * 
     * @param refundId 支付宝退款记录主键
     * @return 结果
     */
    public int deleteAlipayRefundRecordByRefundId(Long refundId);

    /**
     * 根据退款单号查询退款记录
     * 
     * @param refundNo 退款单号
     * @return 退款记录
     */
    public AlipayRefundRecord selectByRefundNo(String refundNo);

    /**
     * 根据订单号查询退款记录列表
     * 
     * @param orderNo 订单号
     * @return 退款记录列表
     */
    public List<AlipayRefundRecord> selectByOrderNo(String orderNo);
}
