package com.ruoyi.issue.pay.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.pay.mapper.AlipayNotifyRecordMapper;
import com.ruoyi.issue.pay.domain.AlipayNotifyRecord;
import com.ruoyi.issue.pay.service.AlipayNotifyRecordService;

/**
 * 支付宝通知记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class AlipayNotifyRecordServiceImpl implements AlipayNotifyRecordService {
    
    @Autowired
    private AlipayNotifyRecordMapper alipayNotifyRecordMapper;

    /**
     * 查询支付宝通知记录
     * 
     * @param notifyId 支付宝通知记录主键
     * @return 支付宝通知记录
     */
    @Override
    public AlipayNotifyRecord selectAlipayNotifyRecordByNotifyId(Long notifyId) {
        return alipayNotifyRecordMapper.selectAlipayNotifyRecordByNotifyId(notifyId);
    }

    /**
     * 查询支付宝通知记录列表
     * 
     * @param alipayNotifyRecord 支付宝通知记录
     * @return 支付宝通知记录
     */
    @Override
    public List<AlipayNotifyRecord> selectAlipayNotifyRecordList(AlipayNotifyRecord alipayNotifyRecord) {
        return alipayNotifyRecordMapper.selectAlipayNotifyRecordList(alipayNotifyRecord);
    }

    /**
     * 新增支付宝通知记录
     * 
     * @param alipayNotifyRecord 支付宝通知记录
     * @return 结果
     */
    @Override
    public int insertAlipayNotifyRecord(AlipayNotifyRecord alipayNotifyRecord) {
        alipayNotifyRecord.setCreateTime(LocalDateTime.now());
        alipayNotifyRecord.setUpdateTime(LocalDateTime.now());
        return alipayNotifyRecordMapper.insertAlipayNotifyRecord(alipayNotifyRecord);
    }

    /**
     * 修改支付宝通知记录
     * 
     * @param alipayNotifyRecord 支付宝通知记录
     * @return 结果
     */
    @Override
    public int updateAlipayNotifyRecord(AlipayNotifyRecord alipayNotifyRecord) {
        alipayNotifyRecord.setUpdateTime(LocalDateTime.now());
        return alipayNotifyRecordMapper.updateAlipayNotifyRecord(alipayNotifyRecord);
    }

    /**
     * 批量删除支付宝通知记录
     * 
     * @param notifyIds 需要删除的支付宝通知记录主键
     * @return 结果
     */
    @Override
    public int deleteAlipayNotifyRecordByNotifyIds(Long[] notifyIds) {
        return alipayNotifyRecordMapper.deleteAlipayNotifyRecordByNotifyIds(notifyIds);
    }

    /**
     * 删除支付宝通知记录信息
     * 
     * @param notifyId 支付宝通知记录主键
     * @return 结果
     */
    @Override
    public int deleteAlipayNotifyRecordByNotifyId(Long notifyId) {
        return alipayNotifyRecordMapper.deleteAlipayNotifyRecordByNotifyId(notifyId);
    }

    /**
     * 根据订单号和通知类型查询通知记录
     * 
     * @param orderNo 订单号
     * @param notifyType 通知类型
     * @return 通知记录列表
     */
    @Override
    public List<AlipayNotifyRecord> selectByOrderNoAndType(String orderNo, String notifyType) {
        return alipayNotifyRecordMapper.selectByOrderNoAndType(orderNo, notifyType);
    }
}
