package com.ruoyi.issue.pay.mapper;

import java.util.List;
import com.ruoyi.issue.pay.domain.AlipayNotifyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 支付宝通知记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Mapper
public interface AlipayNotifyRecordMapper extends BaseMapper<AlipayNotifyRecord> {
    
    /**
     * 查询支付宝通知记录
     * 
     * @param notifyId 支付宝通知记录主键
     * @return 支付宝通知记录
     */
    public AlipayNotifyRecord selectAlipayNotifyRecordByNotifyId(Long notifyId);

    /**
     * 查询支付宝通知记录列表
     * 
     * @param alipayNotifyRecord 支付宝通知记录
     * @return 支付宝通知记录集合
     */
    public List<AlipayNotifyRecord> selectAlipayNotifyRecordList(AlipayNotifyRecord alipayNotifyRecord);

    /**
     * 新增支付宝通知记录
     * 
     * @param alipayNotifyRecord 支付宝通知记录
     * @return 结果
     */
    public int insertAlipayNotifyRecord(AlipayNotifyRecord alipayNotifyRecord);

    /**
     * 修改支付宝通知记录
     * 
     * @param alipayNotifyRecord 支付宝通知记录
     * @return 结果
     */
    public int updateAlipayNotifyRecord(AlipayNotifyRecord alipayNotifyRecord);

    /**
     * 删除支付宝通知记录
     * 
     * @param notifyId 支付宝通知记录主键
     * @return 结果
     */
    public int deleteAlipayNotifyRecordByNotifyId(Long notifyId);

    /**
     * 批量删除支付宝通知记录
     * 
     * @param notifyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAlipayNotifyRecordByNotifyIds(Long[] notifyIds);

    /**
     * 根据订单号和通知类型查询通知记录
     * 
     * @param orderNo 订单号
     * @param notifyType 通知类型
     * @return 通知记录列表
     */
    public List<AlipayNotifyRecord> selectByOrderNoAndType(String orderNo, String notifyType);
}
