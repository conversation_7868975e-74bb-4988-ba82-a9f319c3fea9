package com.ruoyi.issue.pay.controller;

import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.v3.model.AlipayTradeCloseResponseModel;
import com.alipay.v3.model.AlipayTradeQueryResponseModel;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.pay.domain.DigAssetOrder;
import com.ruoyi.issue.pay.domain.AlipayRefundRecord;
import com.ruoyi.issue.pay.domain.AlipayNotifyRecord;
import com.ruoyi.issue.pay.service.AlipayService;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.issue.pay.service.AlipayV2Service;
import com.ruoyi.issue.pay.service.DigAssetOrderService;
import com.ruoyi.issue.pay.service.AlipayRefundRecordService;
import com.ruoyi.issue.pay.service.AlipayNotifyRecordService;
import com.ruoyi.issue.common.constant.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@Api(value = "支付管理", tags = "支付管理")
@RequestMapping("/h5/pay")
@RequiredArgsConstructor
@Slf4j
public class PayController {

    private final AlipayService alipayService;
    private final AlipayV2Service alipayV2Service;
    private final DigAssetOrderService digAssetOrderService;
    private final AlipayRefundRecordService alipayRefundRecordService;
    private final AlipayNotifyRecordService alipayNotifyRecordService;

    /**
     * 创建支付宝支付
     * @param orderNo 订单ID
     * @return 支付页面HTML
     */
    @PostMapping("/createAlipay")
    @ApiOperation("创建支付宝支付")
    public String createAlipayPayment(String orderNo) {
        DigAssetOrder digAssetOrder = digAssetOrderService.selectByOrderNo(orderNo);
        try {
            return alipayService.createWapPayment(digAssetOrder.getOrderNo(), String.valueOf(digAssetOrder.getOrderAmount()), digAssetOrder.getAssetName());
        } catch (Exception e) {
            return "支付创建失败: " + e.getMessage();
        }
    }

    /**
     * 查询订单状态
     * @param orderNo 订单号
     * @return 订单状态
     */
    @PostMapping("/queryOrder")
    @ApiOperation("查询订单状态")
    public AjaxResult queryOrder(String orderNo) {
        try {
            String response = alipayV2Service.queryOrder(orderNo, null);
            return AjaxResult.success( response);
        } catch (Exception e) {
            log.error("查询订单失败: " + e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/closeOrder")
    @ApiOperation("关闭订单")
    public AjaxResult closeOrder(String orderNo) {
        try {
            AlipayTradeQueryResponseModel query = alipayService.queryOrder(orderNo, null);
            if ("WAIT_BUYER_PAY".equals(query.getTradeStatus())) {
                AlipayTradeCloseResponseModel response = alipayService.closeOrder(orderNo, null, "admin");
                return AjaxResult.success("订单关闭成功",response);
            }else {
                return AjaxResult.error("订单已支付");
            }
        }catch (Exception e){
            log.error("订单关闭失败: " + e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 统一收单交易退款接口
     * @param orderNo 商户订单号
     * @param tradeNo 支付宝交易号（可选，与orderNo二选一）
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    @PostMapping("/refund")
    @ApiOperation("统一收单交易退款")
    public AjaxResult refundOrder(
            @ApiParam(value = "商户订单号") @RequestParam(required = false) String orderNo,
            @ApiParam(value = "支付宝交易号") @RequestParam(required = false) String tradeNo,
            @ApiParam(value = "退款金额", required = true) @RequestParam BigDecimal refundAmount,
            @ApiParam(value = "退款原因") @RequestParam(required = false) String refundReason) {

        // 参数验证
        if ((orderNo == null || orderNo.trim().isEmpty()) && (tradeNo == null || tradeNo.trim().isEmpty())) {
            return AjaxResult.error("商户订单号和支付宝交易号至少需要提供一个");
        }

        AlipayRefundRecord refundRecord = new AlipayRefundRecord();
        try {
            DigAssetOrder order = null;

            // 优先使用商户订单号查询订单信息
            if (orderNo != null && !orderNo.trim().isEmpty()) {
                order = digAssetOrderService.selectByOrderNo(orderNo);
                if (order == null) {
                    return AjaxResult.error("商户订单号对应的订单不存在");
                }
            } else if (tradeNo != null && !tradeNo.trim().isEmpty()) {
                // 如果没有提供商户订单号，尝试通过支付宝交易号查询
                // 注意：这里需要先通过支付宝接口查询订单信息获取商户订单号
                try {
                    AlipayTradeQueryResponseModel queryResult = alipayService.queryOrder(null, tradeNo);
                    if (queryResult != null && queryResult.getOutTradeNo() != null) {
                        orderNo = queryResult.getOutTradeNo();
                        order = digAssetOrderService.selectByOrderNo(orderNo);
                    }
                } catch (Exception e) {
                    log.error("通过支付宝交易号查询订单失败: " + e.getMessage(), e);
                    return AjaxResult.error("通过支付宝交易号查询订单失败");
                }

                if (order == null) {
                    return AjaxResult.error("支付宝交易号对应的订单不存在");
                }
            }

            // 验证退款金额
            if (refundAmount.compareTo(order.getOrderAmount()) > 0) {
                return AjaxResult.error("退款金额不能大于订单金额");
            }

            // 生成退款单号
            String refundNo = "RF" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8);

            // 构建退款记录
            refundRecord.setOrderNo(orderNo);
            refundRecord.setTradeNo(tradeNo); // 设置支付宝交易号
            refundRecord.setRefundNo(refundNo);
            refundRecord.setRefundAmount(refundAmount);
            refundRecord.setRefundReason(refundReason);
            refundRecord.setRefundStatus("PROCESSING");
            refundRecord.setCreateBy("system");

            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            if (orderNo != null && !orderNo.trim().isEmpty()) {
                requestData.put("out_trade_no", orderNo);
            }
            if (tradeNo != null && !tradeNo.trim().isEmpty()) {
                requestData.put("trade_no", tradeNo);
            }
            requestData.put("refund_amount", refundAmount.toString());
            requestData.put("refund_reason", refundReason);
            requestData.put("out_request_no", refundNo);

            ObjectMapper objectMapper = new ObjectMapper();
            refundRecord.setRequestData(objectMapper.writeValueAsString(requestData));

            // 在调用支付宝接口之前，将订单状态修改为退款中
            order.setStatusCd(Constants.DIG_ORDER_STATE_REFUNDING);
            digAssetOrderService.updateDigAssetOrder(order);
            log.info("订单状态已修改为退款中，订单号: {}", orderNo);

            // 调用支付宝退款接口
            String response = alipayService.refundOrder(
                orderNo, tradeNo, refundAmount.toString(), refundReason, refundNo);

            // 处理响应结果
            if (response != null && !response.isEmpty()) {
                refundRecord.setResponseData(response);

                // 解析响应JSON判断退款状态
                if (response.contains("\"code\":\"10000\"")) {
                    refundRecord.setRefundStatus("SUCCESS");
                    // 从响应中提取交易号等信息
                    if (response.contains("\"trade_no\":")) {
                        String responseTradeNo = extractJsonValue(response, "trade_no");
                        if (responseTradeNo != null && !responseTradeNo.isEmpty()) {
                            refundRecord.setTradeNo(responseTradeNo);
                        }
                    }

                    // 注意：这里的SUCCESS只是表示退款申请提交成功，不是退款完成
                    // 订单状态保持为UNREFUND，等待异步通知或查询接口确认退款完成
                    log.info("退款申请提交成功，等待支付宝处理，订单号: {}", orderNo);
                } else {
                    refundRecord.setRefundStatus("FAILED");
                    String subMsg = extractJsonValue(response, "sub_msg");
                    refundRecord.setErrorMsg("退款失败：" + (subMsg != null ? subMsg : "未知错误"));
                }
            } else {
                refundRecord.setRefundStatus("FAILED");
                refundRecord.setErrorMsg("支付宝接口调用失败");
            }

            // 保存退款记录
            alipayRefundRecordService.insertAlipayRefundRecord(refundRecord);

            if ("SUCCESS".equals(refundRecord.getRefundStatus())) {
                return AjaxResult.success("退款成功", response);
            } else {
                return AjaxResult.error(refundRecord.getErrorMsg());
            }

        } catch (Exception e) {
            log.error("退款失败: " + e.getMessage(), e);

            // 更新退款记录状态
            if (refundRecord.getRefundId() == null) {
                // 如果记录还没有保存，先保存
                refundRecord.setRefundStatus("FAILED");
                refundRecord.setErrorMsg("系统异常：" + e.getMessage());
                alipayRefundRecordService.insertAlipayRefundRecord(refundRecord);
            } else {
                refundRecord.setRefundStatus("FAILED");
                refundRecord.setErrorMsg("系统异常：" + e.getMessage());
                alipayRefundRecordService.updateAlipayRefundRecord(refundRecord);
            }

            return AjaxResult.error("退款失败：" + e.getMessage());
        }
    }

    /**
     * 直接退款接口（不依赖本地订单数据）
     * @param orderNo 商户订单号
     * @param tradeNo 支付宝交易号（可选，与orderNo二选一）
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    @PostMapping("/directRefund")
    @ApiOperation("直接退款（不依赖本地订单数据）")
    public AjaxResult directRefund(
            @ApiParam(value = "商户订单号") @RequestParam(required = false) String orderNo,
            @ApiParam(value = "支付宝交易号") @RequestParam(required = false) String tradeNo,
            @ApiParam(value = "退款金额", required = true) @RequestParam BigDecimal refundAmount,
            @ApiParam(value = "退款原因") @RequestParam(required = false) String refundReason) {

        // 参数验证
        if ((orderNo == null || orderNo.trim().isEmpty()) && (tradeNo == null || tradeNo.trim().isEmpty())) {
            return AjaxResult.error("商户订单号和支付宝交易号至少需要提供一个");
        }

        AlipayRefundRecord refundRecord = new AlipayRefundRecord();
        try {
            // 第一步：先查询支付宝订单状态，验证订单是否存在且可退款
            log.info("开始查询支付宝订单状态，orderNo: {}, tradeNo: {}", orderNo, tradeNo);

            // 使用支付宝SDK v4查询订单，避免版本冲突
            String queryResponse = queryOrderByV4(orderNo, tradeNo);
            if (queryResponse == null || queryResponse.isEmpty()) {
                return AjaxResult.error("查询支付宝订单失败，订单可能不存在");
            }

            // 解析查询响应 - AlipayV2Service返回的是完整的响应体，需要解析alipay_trade_query_response节点
            String code = extractNestedJsonValue(queryResponse, "alipay_trade_query_response", "code");
            if (!"10000".equals(code)) {
                String subMsg = extractNestedJsonValue(queryResponse, "alipay_trade_query_response", "sub_msg");
                return AjaxResult.error("查询订单失败：" + (subMsg != null ? subMsg : "未知错误"));
            }

            // 验证订单状态是否可以退款
            String tradeStatus = extractNestedJsonValue(queryResponse, "alipay_trade_query_response", "trade_status");
            if (!"TRADE_SUCCESS".equals(tradeStatus) && !"TRADE_FINISHED".equals(tradeStatus)) {
                return AjaxResult.error("订单状态不支持退款，当前状态: " + tradeStatus);
            }

            // 获取订单的实际信息
            String actualOrderNo = extractNestedJsonValue(queryResponse, "alipay_trade_query_response", "out_trade_no");
            String actualTradeNo = extractNestedJsonValue(queryResponse, "alipay_trade_query_response", "trade_no");
            String totalAmountStr = extractNestedJsonValue(queryResponse, "alipay_trade_query_response", "total_amount");

            if (actualOrderNo == null || actualTradeNo == null || totalAmountStr == null) {
                return AjaxResult.error("订单信息不完整，无法进行退款");
            }

            BigDecimal totalAmount = new BigDecimal(totalAmountStr);

            // 验证退款金额
            if (refundAmount.compareTo(totalAmount) > 0) {
                return AjaxResult.error("退款金额不能大于订单金额，订单金额: " + totalAmount);
            }

            // 生成退款单号
            String refundNo = "DRF" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8);

            // 构建退款记录
            refundRecord.setOrderNo(actualOrderNo);
            refundRecord.setTradeNo(actualTradeNo);
            refundRecord.setRefundNo(refundNo);
            refundRecord.setRefundAmount(refundAmount);
            refundRecord.setRefundReason(refundReason);
            refundRecord.setRefundStatus("PROCESSING");
            refundRecord.setCreateBy("direct_refund");

            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("out_trade_no", actualOrderNo);
            requestData.put("trade_no", actualTradeNo);
            requestData.put("refund_amount", refundAmount.toString());
            requestData.put("refund_reason", refundReason);
            requestData.put("out_request_no", refundNo);
            requestData.put("query_response", queryResponse);

            ObjectMapper objectMapper = new ObjectMapper();
            refundRecord.setRequestData(objectMapper.writeValueAsString(requestData));

            // 第二步：直接调用支付宝退款接口
            log.info("开始调用支付宝退款接口，退款单号: {}", refundNo);
            String response = alipayService.refundOrder(
                actualOrderNo, actualTradeNo, refundAmount.toString(), refundReason, refundNo);

            // 处理响应结果
            if (response != null && !response.isEmpty()) {
                refundRecord.setResponseData(response);

                // 解析响应JSON判断退款状态
                if (response.contains("\"code\":\"10000\"")) {
                    refundRecord.setRefundStatus("SUCCESS");
                    // 从响应中提取交易号等信息
                    if (response.contains("\"trade_no\":")) {
                        String responseTradeNo = extractJsonValue(response, "trade_no");
                        if (responseTradeNo != null && !responseTradeNo.isEmpty()) {
                            refundRecord.setTradeNo(responseTradeNo);
                        }
                    }

                    log.info("直接退款申请提交成功，退款单号: {}", refundNo);
                } else {
                    refundRecord.setRefundStatus("FAILED");
                    String subMsg = extractJsonValue(response, "sub_msg");
                    refundRecord.setErrorMsg("退款失败：" + (subMsg != null ? subMsg : "未知错误"));
                }
            } else {
                refundRecord.setRefundStatus("FAILED");
                refundRecord.setErrorMsg("支付宝接口调用失败");
            }

            // 保存退款记录
            alipayRefundRecordService.insertAlipayRefundRecord(refundRecord);

            if ("SUCCESS".equals(refundRecord.getRefundStatus())) {
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("refund_no", refundNo);
                resultData.put("order_no", actualOrderNo);
                resultData.put("trade_no", actualTradeNo);
                resultData.put("refund_amount", refundAmount);
                resultData.put("total_amount", totalAmount);
                resultData.put("alipay_response", response);

                return AjaxResult.success("直接退款申请提交成功", resultData);
            } else {
                return AjaxResult.error(refundRecord.getErrorMsg());
            }

        } catch (Exception e) {
            log.error("直接退款失败: " + e.getMessage(), e);

            // 更新退款记录状态
            if (refundRecord.getRefundId() == null) {
                // 如果记录还没有保存，先保存
                refundRecord.setRefundStatus("FAILED");
                refundRecord.setErrorMsg("系统异常：" + e.getMessage());
                alipayRefundRecordService.insertAlipayRefundRecord(refundRecord);
            } else {
                refundRecord.setRefundStatus("FAILED");
                refundRecord.setErrorMsg("系统异常：" + e.getMessage());
                alipayRefundRecordService.updateAlipayRefundRecord(refundRecord);
            }

            return AjaxResult.error("直接退款失败：" + e.getMessage());
        }
    }

    /**
     * 统一收单交易退款查询接口
     * @param refundNo 退款单号
     * @return 退款查询结果
     */
    @PostMapping("/queryRefund")
    @ApiOperation("统一收单交易退款查询")
    public AjaxResult queryRefund(
            @ApiParam(value = "退款单号", required = true) @RequestParam String refundNo) {

        try {
            // 查询本地退款记录
            AlipayRefundRecord refundRecord = alipayRefundRecordService.selectByRefundNo(refundNo);
            if (refundRecord == null) {
                return AjaxResult.error("退款记录不存在");
            }

            // 调用支付宝退款查询接口
            String response = alipayService.queryRefund(
                refundRecord.getOrderNo(), refundRecord.getTradeNo(), refundNo);

            if (response != null && !response.isEmpty()) {
                refundRecord.setResponseData(response);
                refundRecord.setUpdateBy("system");

                // 根据查询结果更新退款状态
                if (response.contains("\"code\":\"10000\"")) {
                    String refundStatus = extractJsonValue(response, "refund_status");
                    if (refundStatus != null) {
                        switch (refundStatus) {
                            case "REFUND_SUCCESS":
                                refundRecord.setRefundStatus("SUCCESS");
                                // 退款成功，更新订单状态为退款完成
                                DigAssetOrder order = digAssetOrderService.selectByOrderNo(refundRecord.getOrderNo());
                                if (order != null) {
                                    order.setStatusCd(Constants.DIG_ORDER_STATE_REFUNDED);
                                    digAssetOrderService.updateDigAssetOrder(order);
                                    log.info("退款查询确认成功，订单状态已修改为退款完成，订单号: {}", refundRecord.getOrderNo());
                                }
                                break;
                            case "REFUND_PROCESSING":
                                refundRecord.setRefundStatus("PROCESSING");
                                break;
                            default:
                                refundRecord.setRefundStatus("FAILED");
                                break;
                        }
                    }
                } else {
                    String subMsg = extractJsonValue(response, "sub_msg");
                    refundRecord.setErrorMsg("查询失败：" + (subMsg != null ? subMsg : "未知错误"));
                }

                alipayRefundRecordService.updateAlipayRefundRecord(refundRecord);

                return AjaxResult.success("查询成功", response);
            } else {
                return AjaxResult.error("支付宝接口调用失败");
            }

        } catch (Exception e) {
            log.error("退款查询失败: " + e.getMessage(), e);
            return AjaxResult.error("退款查询失败：" + e.getMessage());
        }
    }

    /**
     * 收单退款冲退完成通知接口
     * @param request HTTP请求对象
     * @return 通知处理结果
     */
    @PostMapping("/refundNotify")
    @ApiOperation("收单退款冲退完成通知")
    public String refundNotify(HttpServletRequest request) {

        AlipayNotifyRecord notifyRecord = new AlipayNotifyRecord();
        try {
            // 获取支付宝POST过来反馈信息
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();

            for (String name : requestParams.keySet()) {
                String[] values = requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }

            // 构建通知记录
            ObjectMapper objectMapper = new ObjectMapper();
            String notifyData = objectMapper.writeValueAsString(params);

            notifyRecord.setNotifyType("REFUND");
            notifyRecord.setOrderNo(params.get("out_trade_no"));
            notifyRecord.setTradeNo(params.get("trade_no"));
            notifyRecord.setRefundNo(params.get("out_request_no"));
            notifyRecord.setNotifyData(notifyData);
            notifyRecord.setNotifyStatus("PENDING");
            notifyRecord.setRetryCount(0);

            // 保存通知记录
            alipayNotifyRecordService.insertAlipayNotifyRecord(notifyRecord);

            // 验证通知的合法性（这里简化处理，实际应该验证签名）
            String tradeStatus = params.get("refund_status");
            String outRequestNo = params.get("out_request_no");

            if (outRequestNo != null && !outRequestNo.isEmpty()) {
                // 查询对应的退款记录
                AlipayRefundRecord refundRecord = alipayRefundRecordService.selectByRefundNo(outRequestNo);
                if (refundRecord != null) {
                    // 更新退款记录状态
                    if ("REFUND_SUCCESS".equals(tradeStatus)) {
                        refundRecord.setRefundStatus("SUCCESS");

                        // 退款成功，更新订单状态为退款完成
                        DigAssetOrder order = digAssetOrderService.selectByOrderNo(refundRecord.getOrderNo());
                        if (order != null) {
                            order.setStatusCd(Constants.DIG_ORDER_STATE_REFUNDED);
                            digAssetOrderService.updateDigAssetOrder(order);
                            log.info("收到退款成功通知，订单状态已修改为退款完成，订单号: {}", refundRecord.getOrderNo());
                        }
                    } else if ("REFUND_CLOSED".equals(tradeStatus)) {
                        refundRecord.setRefundStatus("FAILED");
                        refundRecord.setErrorMsg("退款关闭");
                    }
                    refundRecord.setUpdateBy("alipay_notify");
                    alipayRefundRecordService.updateAlipayRefundRecord(refundRecord);

                    // 更新通知记录状态
                    notifyRecord.setNotifyStatus("SUCCESS");
                    notifyRecord.setProcessResult("退款记录状态已更新");
                } else {
                    notifyRecord.setNotifyStatus("FAILED");
                    notifyRecord.setErrorMsg("未找到对应的退款记录");
                }
            } else {
                notifyRecord.setNotifyStatus("FAILED");
                notifyRecord.setErrorMsg("缺少退款单号参数");
            }

            alipayNotifyRecordService.updateAlipayNotifyRecord(notifyRecord);

            // 返回成功标识给支付宝
            return "success";

        } catch (Exception e) {
            log.error("处理退款通知失败: " + e.getMessage(), e);

            // 更新通知记录状态
            if (notifyRecord.getNotifyId() != null) {
                notifyRecord.setNotifyStatus("FAILED");
                notifyRecord.setErrorMsg("系统异常：" + e.getMessage());
                alipayNotifyRecordService.updateAlipayNotifyRecord(notifyRecord);
            }

            return "fail";
        }
    }

    /**
     * 使用支付宝SDK v4查询订单状态
     * @param outTradeNo 商户订单号
     * @param tradeNo 支付宝交易号
     * @return 查询结果JSON字符串
     */
    private String queryOrderByV4(String outTradeNo, String tradeNo) {
        try {
            // 使用AlipayV2Service的新方法来查询订单，支持支付宝交易号
            String response = alipayV2Service.queryOrderWithTradeNo(outTradeNo, tradeNo);
            return response;
        } catch (Exception e) {
            log.error("使用SDK v4查询订单失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从嵌套JSON字符串中提取指定字段的值
     * @param jsonStr JSON字符串
     * @param parentNode 父节点名
     * @param fieldName 字段名
     * @return 字段值
     */
    private String extractNestedJsonValue(String jsonStr, String parentNode, String fieldName) {
        try {
            if (jsonStr == null || jsonStr.isEmpty()) {
                return null;
            }

            // 先提取父节点的内容
            String parentPattern = "\"" + parentNode + "\":\\s*\\{([^}]+)\\}";
            java.util.regex.Pattern parentP = java.util.regex.Pattern.compile(parentPattern);
            java.util.regex.Matcher parentM = parentP.matcher(jsonStr);

            if (parentM.find()) {
                String parentContent = parentM.group(1);
                return extractJsonValue("{" + parentContent + "}", fieldName);
            }

            return null;
        } catch (Exception e) {
            log.error("解析嵌套JSON字段失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从JSON字符串中提取指定字段的值
     * @param jsonStr JSON字符串
     * @param fieldName 字段名
     * @return 字段值
     */
    private String extractJsonValue(String jsonStr, String fieldName) {
        try {
            if (jsonStr == null || jsonStr.isEmpty()) {
                return null;
            }

            String pattern = "\"" + fieldName + "\":\"([^\"]+)\"";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(jsonStr);

            if (m.find()) {
                return m.group(1);
            }

            // 尝试匹配非字符串值（数字、布尔值等）
            pattern = "\"" + fieldName + "\":([^,}]+)";
            p = java.util.regex.Pattern.compile(pattern);
            m = p.matcher(jsonStr);

            if (m.find()) {
                return m.group(1).trim();
            }

            return null;
        } catch (Exception e) {
            log.error("解析JSON字段失败: " + e.getMessage(), e);
            return null;
        }
    }
}
