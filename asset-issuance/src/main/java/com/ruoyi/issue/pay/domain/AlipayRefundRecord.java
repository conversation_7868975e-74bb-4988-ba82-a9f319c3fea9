package com.ruoyi.issue.pay.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;

/**
 * 支付宝退款记录对象 alipay_refund_record
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@ApiModel(value = "支付宝退款记录对象")
@TableName("alipay_refund_record")
public class AlipayRefundRecord {
    private static final long serialVersionUID = 1L;

    /** 退款记录ID */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "退款记录ID")
    private Long refundId;

    /** 商户订单号 */
    @Excel(name = "商户订单号")
    @ApiModelProperty(value = "商户订单号")
    private String orderNo;

    /** 支付宝交易号 */
    @Excel(name = "支付宝交易号")
    @ApiModelProperty(value = "支付宝交易号")
    private String tradeNo;

    /** 退款单号(商户生成) */
    @Excel(name = "退款单号")
    @ApiModelProperty(value = "退款单号(商户生成)")
    private String refundNo;

    /** 支付宝退款单号 */
    @Excel(name = "支付宝退款单号")
    @ApiModelProperty(value = "支付宝退款单号")
    private String alipayRefundNo;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /** 退款原因 */
    @Excel(name = "退款原因")
    @ApiModelProperty(value = "退款原因")
    private String refundReason;

    /** 退款状态 */
    @Excel(name = "退款状态")
    @ApiModelProperty(value = "退款状态(PROCESSING-处理中,SUCCESS-成功,FAILED-失败)")
    private String refundStatus;

    /** 退款请求数据 */
    @ApiModelProperty(value = "退款请求数据(JSON格式)")
    private String requestData;

    /** 退款响应数据 */
    @ApiModelProperty(value = "退款响应数据(JSON格式)")
    private String responseData;

    /** 错误信息 */
    @Excel(name = "错误信息")
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
