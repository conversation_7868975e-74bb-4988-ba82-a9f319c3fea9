package com.ruoyi.issue.pay.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.pay.mapper.AlipayRefundRecordMapper;
import com.ruoyi.issue.pay.domain.AlipayRefundRecord;
import com.ruoyi.issue.pay.service.AlipayRefundRecordService;

/**
 * 支付宝退款记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class AlipayRefundRecordServiceImpl implements AlipayRefundRecordService {
    
    @Autowired
    private AlipayRefundRecordMapper alipayRefundRecordMapper;

    /**
     * 查询支付宝退款记录
     * 
     * @param refundId 支付宝退款记录主键
     * @return 支付宝退款记录
     */
    @Override
    public AlipayRefundRecord selectAlipayRefundRecordByRefundId(Long refundId) {
        return alipayRefundRecordMapper.selectAlipayRefundRecordByRefundId(refundId);
    }

    /**
     * 查询支付宝退款记录列表
     * 
     * @param alipayRefundRecord 支付宝退款记录
     * @return 支付宝退款记录
     */
    @Override
    public List<AlipayRefundRecord> selectAlipayRefundRecordList(AlipayRefundRecord alipayRefundRecord) {
        return alipayRefundRecordMapper.selectAlipayRefundRecordList(alipayRefundRecord);
    }

    /**
     * 新增支付宝退款记录
     * 
     * @param alipayRefundRecord 支付宝退款记录
     * @return 结果
     */
    @Override
    public int insertAlipayRefundRecord(AlipayRefundRecord alipayRefundRecord) {
        alipayRefundRecord.setCreateTime(LocalDateTime.now());
        alipayRefundRecord.setUpdateTime(LocalDateTime.now());
        return alipayRefundRecordMapper.insertAlipayRefundRecord(alipayRefundRecord);
    }

    /**
     * 修改支付宝退款记录
     * 
     * @param alipayRefundRecord 支付宝退款记录
     * @return 结果
     */
    @Override
    public int updateAlipayRefundRecord(AlipayRefundRecord alipayRefundRecord) {
        alipayRefundRecord.setUpdateTime(LocalDateTime.now());
        return alipayRefundRecordMapper.updateAlipayRefundRecord(alipayRefundRecord);
    }

    /**
     * 批量删除支付宝退款记录
     * 
     * @param refundIds 需要删除的支付宝退款记录主键
     * @return 结果
     */
    @Override
    public int deleteAlipayRefundRecordByRefundIds(Long[] refundIds) {
        return alipayRefundRecordMapper.deleteAlipayRefundRecordByRefundIds(refundIds);
    }

    /**
     * 删除支付宝退款记录信息
     * 
     * @param refundId 支付宝退款记录主键
     * @return 结果
     */
    @Override
    public int deleteAlipayRefundRecordByRefundId(Long refundId) {
        return alipayRefundRecordMapper.deleteAlipayRefundRecordByRefundId(refundId);
    }

    /**
     * 根据退款单号查询退款记录
     * 
     * @param refundNo 退款单号
     * @return 退款记录
     */
    @Override
    public AlipayRefundRecord selectByRefundNo(String refundNo) {
        return alipayRefundRecordMapper.selectByRefundNo(refundNo);
    }

    /**
     * 根据订单号查询退款记录列表
     * 
     * @param orderNo 订单号
     * @return 退款记录列表
     */
    @Override
    public List<AlipayRefundRecord> selectByOrderNo(String orderNo) {
        return alipayRefundRecordMapper.selectByOrderNo(orderNo);
    }
}
